{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/search/search.vue?fd81", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/search/search.vue?3997", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/search/search.vue?5546", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/search/search.vue?90f6", "uni-app:///pages/search/search.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/search/search.vue?1870", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/search/search.vue?7eea"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "keyword", "showSuggestions", "suggestions", "hotKeywords", "searchHistory", "showResults", "searchResults", "totalCount", "loading", "refreshing", "hasMore", "pageNum", "pageSize", "sortBy", "showSortPicker", "sortOptions", "label", "value", "computed", "sortText", "onLoad", "methods", "formatSalary", "formatTime", "onInput", "onSearch", "getSuggestions", "console", "loadHotKeywords", "jobApi", "res", "loadSearchHistory", "saveSearchHistory", "history", "storage", "searchJobs", "isRefresh", "params", "newList", "selectSuggestion", "selectKeyword", "clearKeyword", "clearHistory", "uni", "title", "content", "success", "removeHistory", "onRefresh", "loadMore", "selectSort", "goToJobDetail", "url", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyIx1B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EACAC;IACAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;IACAC;IAEA;IACAC;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA,gCACA,wDACA,4CACA,iCACA;kBACA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACAC;QAAA;MAAA;;MAEA;MACAA;;MAEA;MACA;QACAA;MACA;MAEAC;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;kBACA;kBACA;gBACA;gBAEAC;kBACArC;kBACAW;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAgB;cAAA;gBAAAC;gBAEA;kBACAQ;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;QACAC;QACAC;UACA;YACAZ;YACA;UACA;QACA;MACA;IACA;IAEA;IACAa;MACA;QAAA;MAAA;MACAb;IACA;IAEA;IACAc;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAIA;IACAC;MACAR;QACAS;MACA;IACA;IAEA;IACAC;MACAV;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvXA;AAAA;AAAA;AAAA;AAA2jD,CAAgB,27CAAG,EAAC,C;;;;;;;;;;;ACA/kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/search/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/search/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4cedc0c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/search/search.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.showSuggestions && _vm.suggestions.length > 0\n  var g1 = !_vm.keyword && _vm.hotKeywords.length > 0\n  var g2 = !_vm.keyword && _vm.searchHistory.length > 0\n  var l0 = _vm.showResults\n    ? _vm.__map(_vm.searchResults, function (job, __i3__) {\n        var $orig = _vm.__get_orig(job)\n        var m0 = _vm.formatSalary(job.minSalary, job.maxSalary, job.salaryType)\n        var m1 = _vm.formatTime(job.publishTime)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g3 = _vm.showResults && !_vm.hasMore ? _vm.searchResults.length : null\n  var g4 = _vm.showResults\n    ? !_vm.loading && _vm.searchResults.length === 0 && _vm.showResults\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showSortPicker = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"search-container\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\">\n      <view class=\"search-input-wrapper\">\n        <text class=\"iconfont icon-search\"></text>\n        <input class=\"search-input\" type=\"text\" placeholder=\"搜索职位、公司\" v-model=\"keyword\" @input=\"onInput\"\n          @confirm=\"onSearch\" focus />\n        <text v-if=\"keyword\" class=\"clear-btn\" @click=\"clearKeyword\">×</text>\n      </view>\n      <text class=\"cancel-btn\" @click=\"goBack\">取消</text>\n    </view>\n\n    <!-- 搜索建议 -->\n    <view v-if=\"showSuggestions && suggestions.length > 0\" class=\"suggestions\">\n      <view v-for=\"item in suggestions\" :key=\"item\" class=\"suggestion-item\" @click=\"selectSuggestion(item)\">\n        <text class=\"iconfont icon-search\"></text>\n        <text class=\"suggestion-text\">{{ item }}</text>\n      </view>\n    </view>\n\n    <!-- 热门搜索 -->\n    <view v-if=\"!keyword && hotKeywords.length > 0\" class=\"hot-keywords\">\n      <view class=\"section-title\">热门搜索</view>\n      <view class=\"keywords-list\">\n        <text v-for=\"item in hotKeywords\" :key=\"item\" class=\"keyword-item\" @click=\"selectKeyword(item)\">\n          {{ item }}\n        </text>\n      </view>\n    </view>\n\n    <!-- 搜索历史 -->\n    <view v-if=\"!keyword && searchHistory.length > 0\" class=\"search-history\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">搜索历史</text>\n        <text class=\"clear-history\" @click=\"clearHistory\">清空</text>\n      </view>\n      <view class=\"history-list\">\n        <view v-for=\"item in searchHistory\" :key=\"item\" class=\"history-item\" @click=\"selectKeyword(item)\">\n          <text class=\"iconfont icon-time\"></text>\n          <text class=\"history-text\">{{ item }}</text>\n          <text class=\"remove-btn\" @click.stop=\"removeHistory(item)\">×</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 搜索结果 -->\n    <view v-if=\"showResults\" class=\"search-results\">\n      <view class=\"results-header\">\n        <text class=\"results-count\">找到 {{ totalCount }} 个相关职位</text>\n        <view class=\"sort-btn\" @click=\"showSortPicker = true\">\n          <text>{{ sortText }}</text>\n          <text class=\"iconfont icon-arrow-down\"></text>\n        </view>\n      </view>\n\n      <scroll-view class=\"results-list\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled\n        :refresher-triggered=\"refreshing\" @refresherrefresh=\"onRefresh\">\n        <view v-for=\"job in searchResults\" :key=\"job.id\" class=\"job-item\" @click=\"goToJobDetail(job.id)\">\n          <view class=\"job-header\">\n            <view class=\"job-info\">\n              <text class=\"job-title\">{{ job.title }}</text>\n              <text class=\"salary\">{{ formatSalary(job.minSalary, job.maxSalary, job.salaryType) }}</text>\n            </view>\n            <view class=\"company-logo\">\n              <image :src=\"job.companyLogo || '/static/default-company.png'\" mode=\"aspectFill\"></image>\n            </view>\n          </view>\n\n          <view class=\"job-tags\">\n            <text v-for=\"tag in job.tags\" :key=\"tag\" class=\"tag\">\n              {{ tag }}\n            </text>\n          </view>\n\n          <view class=\"job-meta\">\n            <view class=\"meta-item\">\n              <text class=\"iconfont icon-location\"></text>\n              <text>{{ job.location }}</text>\n            </view>\n            <view class=\"meta-item\">\n              <text class=\"iconfont icon-experience\"></text>\n              <text>{{ job.experience }}</text>\n            </view>\n            <view class=\"meta-item\">\n              <text class=\"iconfont icon-education\"></text>\n              <text>{{ job.education }}</text>\n            </view>\n          </view>\n\n          <view class=\"job-footer\">\n            <view class=\"company-info\">\n              <text class=\"company-name\">{{ job.companyName }}</text>\n              <text class=\"company-scale\">{{ job.companyScale }}</text>\n            </view>\n            <view class=\"publish-time\">\n              <text>{{ formatTime(job.publishTime) }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 加载更多 -->\n        <view v-if=\"hasMore\" class=\"load-more\">\n          <text>{{ loading ? '加载中...' : '上拉加载更多' }}</text>\n        </view>\n        <view v-else-if=\"searchResults.length > 0\" class=\"no-more\">\n          <text>没有更多数据了</text>\n        </view>\n\n        <!-- 空状态 -->\n        <view v-if=\"!loading && searchResults.length === 0 && showResults\" class=\"empty-state\">\n          <image src=\"/static/empty-search.png\" class=\"empty-img\"></image>\n          <text class=\"empty-text\">没有找到相关职位</text>\n          <text class=\"empty-tip\">试试其他关键词吧</text>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 排序选择弹窗 -->\n    <u-popup v-model=\"showSortPicker\" mode=\"bottom\" height=\"40%\" border-radius=\"20\">\n      <view class=\"sort-picker\">\n        <view class=\"picker-header\">\n          <text class=\"title\">排序方式</text>\n        </view>\n        <view class=\"sort-options\">\n          <view v-for=\"option in sortOptions\" :key=\"option.value\" class=\"sort-option\"\n            :class=\"{ active: sortBy === option.value }\" @click=\"selectSort(option.value)\">\n            <text>{{ option.label }}</text>\n            <text v-if=\"sortBy === option.value\" class=\"iconfont icon-check\"></text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { jobApi } from '@/utils/api.js'\nimport { formatSalary, formatTime, showToast, debounce, storage } from '@/utils/utils.js'\n\nexport default {\n  name: 'Search',\n  data() {\n    return {\n      keyword: '',\n      showSuggestions: false,\n      suggestions: [],\n      hotKeywords: [],\n      searchHistory: [],\n      showResults: false,\n      searchResults: [],\n      totalCount: 0,\n      loading: false,\n      refreshing: false,\n      hasMore: true,\n      pageNum: 1,\n      pageSize: 10,\n      sortBy: 'relevance',\n      showSortPicker: false,\n\n      sortOptions: [\n        { label: '相关度', value: 'relevance' },\n        { label: '发布时间', value: 'publishTime' },\n        { label: '薪资高低', value: 'salary' }\n      ]\n    }\n  },\n  computed: {\n    sortText() {\n      const option = this.sortOptions.find(item => item.value === this.sortBy)\n      return option ? option.label : '排序'\n    }\n  },\n  onLoad() {\n    this.loadHotKeywords()\n    this.loadSearchHistory()\n  },\n  methods: {\n    formatSalary,\n    formatTime,\n\n    // 防抖搜索\n    onInput: debounce(function () {\n      if (this.keyword.trim()) {\n        this.getSuggestions()\n      } else {\n        this.showSuggestions = false\n        this.showResults = false\n      }\n    }, 300),\n\n    // 搜索\n    onSearch() {\n      if (!this.keyword.trim()) return\n\n      this.showSuggestions = false\n      this.showResults = true\n      this.saveSearchHistory(this.keyword.trim())\n      this.searchJobs(true)\n    },\n\n    // 获取搜索建议\n    async getSuggestions() {\n      try {\n        // 这里可以调用搜索建议接口\n        // const res = await jobApi.getSearchSuggestions(this.keyword)\n        // this.suggestions = res.data || []\n\n        // 临时使用本地数据\n        this.suggestions = [\n          `${this.keyword} 相关职位`,\n          `${this.keyword} 公司`,\n          `${this.keyword} 岗位`\n        ]\n        this.showSuggestions = true\n      } catch (error) {\n        console.error('获取搜索建议失败:', error)\n      }\n    },\n\n    // 加载热门关键词\n    async loadHotKeywords() {\n      try {\n        const res = await jobApi.getHotKeywords()\n        if (res.code === 200) {\n          this.hotKeywords = res.data || []\n        }\n      } catch (error) {\n        console.error('加载热门关键词失败:', error)\n        // 使用默认数据\n        this.hotKeywords = ['前端开发', '后端开发', '产品经理', '设计师', '运营', '销售']\n      }\n    },\n\n    // 加载搜索历史\n    loadSearchHistory() {\n      this.searchHistory = storage.get('searchHistory', [])\n    },\n\n    // 保存搜索历史\n    saveSearchHistory(keyword) {\n      let history = storage.get('searchHistory', [])\n\n      // 移除重复项\n      history = history.filter(item => item !== keyword)\n\n      // 添加到开头\n      history.unshift(keyword)\n\n      // 限制数量\n      if (history.length > 10) {\n        history = history.slice(0, 10)\n      }\n\n      storage.set('searchHistory', history)\n      this.searchHistory = history\n    },\n\n    // 搜索职位\n    async searchJobs(isRefresh = false) {\n      if (this.loading) return\n\n      try {\n        this.loading = true\n        if (isRefresh) {\n          this.pageNum = 1\n          this.hasMore = true\n        }\n\n        const params = {\n          keyword: this.keyword.trim(),\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          sortBy: this.sortBy\n        }\n\n        const res = await jobApi.searchJobs(this.keyword.trim(), params)\n\n        if (res.code === 200) {\n          const newList = res.data.list || []\n\n          if (isRefresh) {\n            this.searchResults = newList\n          } else {\n            this.searchResults = [...this.searchResults, ...newList]\n          }\n\n          this.totalCount = res.data.total || 0\n          this.hasMore = newList.length === this.pageSize\n          this.pageNum++\n        } else {\n          showToast(res.msg || '搜索失败')\n        }\n      } catch (error) {\n        console.error('搜索失败:', error)\n        showToast('搜索失败，请重试')\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n\n    // 选择搜索建议\n    selectSuggestion(suggestion) {\n      this.keyword = suggestion\n      this.onSearch()\n    },\n\n    // 选择关键词\n    selectKeyword(keyword) {\n      this.keyword = keyword\n      this.onSearch()\n    },\n\n    // 清空关键词\n    clearKeyword() {\n      this.keyword = ''\n      this.showSuggestions = false\n      this.showResults = false\n    },\n\n    // 清空搜索历史\n    clearHistory() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要清空搜索历史吗？',\n        success: (res) => {\n          if (res.confirm) {\n            storage.remove('searchHistory')\n            this.searchHistory = []\n          }\n        }\n      })\n    },\n\n    // 删除单个历史记录\n    removeHistory(keyword) {\n      this.searchHistory = this.searchHistory.filter(item => item !== keyword)\n      storage.set('searchHistory', this.searchHistory)\n    },\n\n    // 刷新\n    onRefresh() {\n      this.refreshing = true\n      this.searchJobs(true)\n    },\n\n    // 加载更多\n    loadMore() {\n      if (this.hasMore && !this.loading) {\n        this.searchJobs()\n      }\n    },\n\n    // 选择排序方式\n    selectSort(value) {\n      this.sortBy = value\n      this.showSortPicker = false\n      this.onRefresh()\n    },\n\n\n\n    // 跳转到职位详情\n    goToJobDetail(jobId) {\n      uni.navigateTo({\n        url: `/pages/job/detail?id=${jobId}`\n      })\n    },\n\n    // 返回\n    goBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.search-container {\n  height: 100vh;\n  background: #f8f9fa;\n\n  .search-bar {\n    display: flex;\n    align-items: center;\n    padding: 20rpx 32rpx;\n    background: #fff;\n    border-bottom: 2rpx solid #f0f0f0;\n\n    .search-input-wrapper {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      height: 72rpx;\n      padding: 0 24rpx;\n      background: #f8f9fa;\n      border-radius: 36rpx;\n      margin-right: 24rpx;\n\n      .iconfont {\n        font-size: 32rpx;\n        color: #999;\n        margin-right: 16rpx;\n      }\n\n      .search-input {\n        flex: 1;\n        font-size: 28rpx;\n        color: #333;\n      }\n\n      .clear-btn {\n        font-size: 36rpx;\n        color: #999;\n        line-height: 1;\n      }\n    }\n\n    .cancel-btn {\n      font-size: 28rpx;\n      color: #007aff;\n    }\n  }\n\n  .suggestions {\n    background: #fff;\n    border-bottom: 2rpx solid #f0f0f0;\n\n    .suggestion-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx 32rpx;\n      border-bottom: 2rpx solid #f8f9fa;\n\n      .iconfont {\n        font-size: 28rpx;\n        color: #999;\n        margin-right: 16rpx;\n      }\n\n      .suggestion-text {\n        font-size: 28rpx;\n        color: #333;\n      }\n    }\n  }\n\n  .hot-keywords,\n  .search-history {\n    background: #fff;\n    margin-top: 20rpx;\n    padding: 32rpx;\n\n    .section-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 24rpx;\n    }\n\n    .section-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: 24rpx;\n\n      .clear-history {\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n\n    .keywords-list {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 16rpx;\n\n      .keyword-item {\n        padding: 16rpx 24rpx;\n        background: #f8f9fa;\n        color: #666;\n        font-size: 26rpx;\n        border-radius: 32rpx;\n        border: 2rpx solid #e9ecef;\n      }\n    }\n\n    .history-list {\n      .history-item {\n        display: flex;\n        align-items: center;\n        padding: 20rpx 0;\n        border-bottom: 2rpx solid #f8f9fa;\n\n        .iconfont {\n          font-size: 28rpx;\n          color: #999;\n          margin-right: 16rpx;\n        }\n\n        .history-text {\n          flex: 1;\n          font-size: 28rpx;\n          color: #333;\n        }\n\n        .remove-btn {\n          font-size: 32rpx;\n          color: #999;\n          line-height: 1;\n        }\n      }\n    }\n  }\n\n  .search-results {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n\n    .results-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 24rpx 32rpx;\n      background: #fff;\n      border-bottom: 2rpx solid #f0f0f0;\n\n      .results-count {\n        font-size: 28rpx;\n        color: #666;\n      }\n\n      .sort-btn {\n        display: flex;\n        align-items: center;\n        font-size: 28rpx;\n        color: #333;\n\n        .iconfont {\n          font-size: 20rpx;\n          color: #999;\n          margin-left: 8rpx;\n        }\n      }\n    }\n\n    .results-list {\n      flex: 1;\n      padding: 0 32rpx;\n\n      .job-item {\n        background: #fff;\n        border-radius: 16rpx;\n        padding: 32rpx;\n        margin: 24rpx 0;\n        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n\n        .job-header {\n          display: flex;\n          align-items: flex-start;\n          justify-content: space-between;\n          margin-bottom: 24rpx;\n\n          .job-info {\n            flex: 1;\n\n            .job-title {\n              display: block;\n              font-size: 32rpx;\n              font-weight: 600;\n              color: #333;\n              margin-bottom: 12rpx;\n            }\n\n            .salary {\n              display: block;\n              font-size: 28rpx;\n              color: #ff4757;\n              font-weight: 600;\n            }\n          }\n\n          .company-logo {\n            width: 80rpx;\n            height: 80rpx;\n            border-radius: 12rpx;\n            overflow: hidden;\n\n            image {\n              width: 100%;\n              height: 100%;\n            }\n          }\n        }\n\n        .job-tags {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 16rpx;\n          margin-bottom: 24rpx;\n\n          .tag {\n            padding: 8rpx 16rpx;\n            background: #f0f8ff;\n            color: #007aff;\n            font-size: 24rpx;\n            border-radius: 8rpx;\n          }\n        }\n\n        .job-meta {\n          display: flex;\n          align-items: center;\n          gap: 32rpx;\n          margin-bottom: 24rpx;\n\n          .meta-item {\n            display: flex;\n            align-items: center;\n            font-size: 24rpx;\n            color: #666;\n\n            .iconfont {\n              font-size: 24rpx;\n              margin-right: 8rpx;\n            }\n          }\n        }\n\n        .job-footer {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n\n          .company-info {\n            .company-name {\n              font-size: 28rpx;\n              color: #333;\n              margin-right: 16rpx;\n            }\n\n            .company-scale {\n              font-size: 24rpx;\n              color: #666;\n            }\n          }\n\n          .publish-time {\n            font-size: 24rpx;\n            color: #999;\n          }\n        }\n      }\n\n      .load-more,\n      .no-more {\n        text-align: center;\n        padding: 40rpx 0;\n        font-size: 28rpx;\n        color: #999;\n      }\n\n      .empty-state {\n        text-align: center;\n        padding: 120rpx 0;\n\n        .empty-img {\n          width: 200rpx;\n          height: 200rpx;\n          margin-bottom: 40rpx;\n        }\n\n        .empty-text {\n          display: block;\n          font-size: 28rpx;\n          color: #999;\n          margin-bottom: 16rpx;\n        }\n\n        .empty-tip {\n          display: block;\n          font-size: 24rpx;\n          color: #ccc;\n        }\n      }\n    }\n  }\n}\n\n.sort-picker {\n  .picker-header {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 32rpx;\n    border-bottom: 2rpx solid #f0f0f0;\n\n    .title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n  }\n\n  .sort-options {\n    .sort-option {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 32rpx;\n      border-bottom: 2rpx solid #f8f9fa;\n      font-size: 28rpx;\n      color: #333;\n\n      &.active {\n        color: #007aff;\n        background: #f0f8ff;\n      }\n\n      .iconfont {\n        font-size: 32rpx;\n        color: #007aff;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756738041719\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}