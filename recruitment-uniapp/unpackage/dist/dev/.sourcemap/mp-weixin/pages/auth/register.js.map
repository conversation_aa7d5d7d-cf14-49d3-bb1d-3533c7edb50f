{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/register.vue?5fb7", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/register.vue?8f92", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/register.vue?c328", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/register.vue?b9c2", "uni-app:///pages/auth/register.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/register.vue?43e7", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/register.vue?5fb0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "RegionPicker", "data", "loading", "agreed", "codeDisabled", "codeCountdown", "codeTimer", "experienceIndex", "educationIndex", "form", "avatar", "phone", "code", "gender", "age", "region", "<PERSON><PERSON><PERSON>", "minSalary", "max<PERSON><PERSON><PERSON>", "experience", "education", "introduction", "experienceOptions", "educationOptions", "computed", "canSubmit", "codeText", "onUnload", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "uni", "count", "sizeType", "sourceType", "success", "uploadAvatar", "sendCode", "startCountdown", "onGenderChange", "onExperienceChange", "onEducationChange", "onAgreementChange", "showAgreement", "url", "handleRegister", "registerData", "regionId", "authApi", "res", "auth", "setTimeout", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC8I11B;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAZ;QACAa;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,oBACA,SACA,QACA,QACA,QACA,SACA,QACA;MACAC,mBACA,SACA,SACA,MACA,MACA,MACA;IAEA;EACA;EACAC;IACAC;MACA,gCACA,8CACA,+BACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;kBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEA;QACA;QACA;UACAV;UACA;QACA;MACA;IACA;IAEA;IACAW;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAZ;QAAAa;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBAEAC,+CACA;kBACAC,2EACA,yDACA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAEA;kBACA;;kBAEA;kBACAC;kBACAA;kBAEAC;oBACApB;sBACAa;oBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAQ;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChVA;AAAA;AAAA;AAAA;AAA6jD,CAAgB,67CAAG,EAAC,C;;;;;;;;;;;ACAjlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/auth/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/auth/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=6e828681&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=6e828681&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e828681\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/auth/register.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=6e828681&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.form.introduction.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"register-container\">\n    <!-- 头部 -->\n    <view class=\"header\">\n      <text class=\"title\">用户注册</text>\n      <text class=\"subtitle\">完善个人信息，开启求职之旅</text>\n    </view>\n\n    <!-- 注册表单 -->\n    <view class=\"form\">\n      <!-- 头像上传 -->\n      <view class=\"form-item avatar-item\">\n        <text class=\"label\">头像</text>\n        <view class=\"avatar-upload\" @click=\"chooseAvatar\">\n          <image v-if=\"form.avatar\" :src=\"form.avatar\" class=\"avatar-img\" mode=\"aspectFill\"></image>\n          <view v-else class=\"avatar-placeholder\">\n            <text class=\"iconfont icon-camera\"></text>\n            <text class=\"placeholder-text\">上传头像</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 姓名 -->\n      <view class=\"form-item\">\n        <text class=\"label\">姓名 <text class=\"required\">*</text></text>\n        <input class=\"input\" type=\"text\" placeholder=\"请输入真实姓名\" v-model=\"form.name\" />\n      </view>\n\n      <!-- 手机号 -->\n      <view class=\"form-item\">\n        <text class=\"label\">手机号 <text class=\"required\">*</text></text>\n        <input class=\"input\" type=\"number\" placeholder=\"请输入手机号\" v-model=\"form.phone\" maxlength=\"11\" />\n      </view>\n\n      <!-- 验证码 -->\n      <view class=\"form-item\">\n        <text class=\"label\">验证码 <text class=\"required\">*</text></text>\n        <view class=\"code-input\">\n          <input class=\"input\" type=\"number\" placeholder=\"请输入验证码\" v-model=\"form.code\" maxlength=\"6\" />\n          <button class=\"code-btn\" :disabled=\"codeDisabled\" @click=\"sendCode\">\n            {{ codeText }}\n          </button>\n        </view>\n      </view>\n\n      <!-- 性别 -->\n      <view class=\"form-item\">\n        <text class=\"label\">性别</text>\n        <radio-group class=\"radio-group\" @change=\"onGenderChange\">\n          <label class=\"radio-item\">\n            <radio value=\"1\" :checked=\"form.gender === '1'\" color=\"#007aff\" />\n            <text>男</text>\n          </label>\n          <label class=\"radio-item\">\n            <radio value=\"2\" :checked=\"form.gender === '2'\" color=\"#007aff\" />\n            <text>女</text>\n          </label>\n        </radio-group>\n      </view>\n\n      <!-- 年龄 -->\n      <view class=\"form-item\">\n        <text class=\"label\">年龄</text>\n        <input class=\"input\" type=\"number\" placeholder=\"请输入年龄\" v-model=\"form.age\" maxlength=\"2\" />\n      </view>\n\n      <!-- 地区选择 -->\n      <view class=\"form-item\">\n        <text class=\"label\">所在地区</text>\n        <RegionPicker v-model=\"form.region\" />\n      </view>\n\n      <!-- 期望职位 -->\n      <view class=\"form-item\">\n        <text class=\"label\">期望职位</text>\n        <input class=\"input\" type=\"text\" placeholder=\"请输入期望职位\" v-model=\"form.expectedJob\" />\n      </view>\n\n      <!-- 期望薪资 -->\n      <view class=\"form-item\">\n        <text class=\"label\">期望薪资</text>\n        <view class=\"salary-input\">\n          <input class=\"input salary-min\" type=\"number\" placeholder=\"最低\" v-model=\"form.minSalary\" />\n          <text class=\"separator\">-</text>\n          <input class=\"input salary-max\" type=\"number\" placeholder=\"最高\" v-model=\"form.maxSalary\" />\n          <text class=\"unit\">元/月</text>\n        </view>\n      </view>\n\n      <!-- 工作经验 -->\n      <view class=\"form-item\">\n        <text class=\"label\">工作经验</text>\n        <picker :range=\"experienceOptions\" :value=\"experienceIndex\" @change=\"onExperienceChange\">\n          <view class=\"picker-input\">\n            <text class=\"picker-text\">{{ experienceOptions[experienceIndex] || '请选择工作经验' }}</text>\n            <text class=\"iconfont icon-arrow-down\"></text>\n          </view>\n        </picker>\n      </view>\n\n      <!-- 学历 -->\n      <view class=\"form-item\">\n        <text class=\"label\">学历</text>\n        <picker :range=\"educationOptions\" :value=\"educationIndex\" @change=\"onEducationChange\">\n          <view class=\"picker-input\">\n            <text class=\"picker-text\">{{ educationOptions[educationIndex] || '请选择学历' }}</text>\n            <text class=\"iconfont icon-arrow-down\"></text>\n          </view>\n        </picker>\n      </view>\n\n      <!-- 个人简介 -->\n      <view class=\"form-item\">\n        <text class=\"label\">个人简介</text>\n        <textarea class=\"textarea\" placeholder=\"请简单介绍一下自己的工作经历和技能特长\" v-model=\"form.introduction\"\n          maxlength=\"500\"></textarea>\n        <text class=\"char-count\">{{ form.introduction.length }}/500</text>\n      </view>\n    </view>\n\n    <!-- 用户协议 -->\n    <view class=\"agreement\">\n      <checkbox-group @change=\"onAgreementChange\">\n        <checkbox :checked=\"agreed\" color=\"#007aff\" />\n      </checkbox-group>\n      <text class=\"agreement-text\">\n        我已阅读并同意\n        <text class=\"link\" @click=\"showAgreement('user')\">《用户协议》</text>\n        和\n        <text class=\"link\" @click=\"showAgreement('privacy')\">《隐私政策》</text>\n      </text>\n    </view>\n\n    <!-- 注册按钮 -->\n    <button class=\"register-btn\" :disabled=\"!canSubmit\" :loading=\"loading\" @click=\"handleRegister\">\n      立即注册\n    </button>\n  </view>\n</template>\n\n<script>\nimport RegionPicker from '@/components/RegionPicker.vue'\nimport { authApi } from '@/utils/api.js'\nimport { auth, validatePhone, showToast, showLoading, hideLoading } from '@/utils/utils.js'\n\nexport default {\n  name: 'Register',\n  components: {\n    RegionPicker\n  },\n  data() {\n    return {\n      loading: false,\n      agreed: false,\n      codeDisabled: false,\n      codeCountdown: 0,\n      codeTimer: null,\n      experienceIndex: -1,\n      educationIndex: -1,\n      form: {\n        avatar: '',\n        name: '',\n        phone: '',\n        code: '',\n        gender: '',\n        age: '',\n        region: {},\n        expectedJob: '',\n        minSalary: '',\n        maxSalary: '',\n        experience: '',\n        education: '',\n        introduction: ''\n      },\n      experienceOptions: [\n        '应届毕业生',\n        '1年以下',\n        '1-3年',\n        '3-5年',\n        '5-10年',\n        '10年以上'\n      ],\n      educationOptions: [\n        '初中及以下',\n        '高中/中专',\n        '大专',\n        '本科',\n        '硕士',\n        '博士'\n      ]\n    }\n  },\n  computed: {\n    canSubmit() {\n      return this.form.name.trim() &&\n        validatePhone(this.form.phone) &&\n        this.form.code.length === 6 &&\n        this.agreed\n    },\n    codeText() {\n      return this.codeCountdown > 0 ? `${this.codeCountdown}s` : '获取验证码'\n    }\n  },\n  onUnload() {\n    if (this.codeTimer) {\n      clearInterval(this.codeTimer)\n    }\n  },\n  methods: {\n    // 选择头像\n    chooseAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.form.avatar = res.tempFilePaths[0]\n          // 这里可以上传到服务器\n          this.uploadAvatar(res.tempFilePaths[0])\n        }\n      })\n    },\n\n    // 上传头像\n    async uploadAvatar(filePath) {\n      try {\n        showLoading('上传中...')\n        // 这里调用上传接口\n        // const res = await uploadApi.uploadImage(filePath)\n        // this.form.avatar = res.data.url\n      } catch (error) {\n        showToast('头像上传失败')\n      } finally {\n        hideLoading()\n      }\n    },\n\n    // 发送验证码\n    async sendCode() {\n      if (!validatePhone(this.form.phone)) {\n        showToast('请输入正确的手机号')\n        return\n      }\n\n      try {\n        // 这里应该调用发送验证码的接口\n        // await authApi.sendCode(this.form.phone)\n\n        showToast('验证码已发送')\n        this.startCountdown()\n      } catch (error) {\n        showToast('发送验证码失败')\n      }\n    },\n\n    // 开始倒计时\n    startCountdown() {\n      this.codeDisabled = true\n      this.codeCountdown = 60\n\n      this.codeTimer = setInterval(() => {\n        this.codeCountdown--\n        if (this.codeCountdown <= 0) {\n          clearInterval(this.codeTimer)\n          this.codeDisabled = false\n        }\n      }, 1000)\n    },\n\n    // 性别变化\n    onGenderChange(e) {\n      this.form.gender = e.detail.value\n    },\n\n    // 工作经验变化\n    onExperienceChange(e) {\n      this.experienceIndex = e.detail.value\n      this.form.experience = this.experienceOptions[e.detail.value]\n    },\n\n    // 学历变化\n    onEducationChange(e) {\n      this.educationIndex = e.detail.value\n      this.form.education = this.educationOptions[e.detail.value]\n    },\n\n    // 同意协议变化\n    onAgreementChange(e) {\n      this.agreed = e.detail.value.length > 0\n    },\n\n    // 显示协议\n    showAgreement(type) {\n      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'\n      uni.navigateTo({ url })\n    },\n\n    // 注册\n    async handleRegister() {\n      if (!this.canSubmit) return\n\n      try {\n        this.loading = true\n\n        const registerData = {\n          ...this.form,\n          regionId: (this.form.region.district && this.form.region.district.id) ||\n            (this.form.region.city && this.form.region.city.id) ||\n            (this.form.region.province && this.form.region.province.id)\n        }\n\n        const res = await authApi.register(registerData)\n\n        if (res.code === 200) {\n          showToast('注册成功', 'success')\n\n          // 自动登录\n          auth.setToken(res.data.token)\n          auth.setUserInfo(res.data.userInfo)\n\n          setTimeout(() => {\n            uni.switchTab({\n              url: '/pages/index/index'\n            })\n          }, 1000)\n        } else {\n          showToast(res.msg || '注册失败')\n        }\n      } catch (error) {\n        console.error('注册失败:', error)\n        showToast('注册失败，请重试')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.register-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding: 40rpx;\n\n  .header {\n    text-align: center;\n    margin-bottom: 60rpx;\n\n    .title {\n      display: block;\n      font-size: 48rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 16rpx;\n    }\n\n    .subtitle {\n      display: block;\n      font-size: 28rpx;\n      color: #666;\n    }\n  }\n\n  .form {\n    background: #fff;\n    border-radius: 16rpx;\n    padding: 40rpx;\n    margin-bottom: 40rpx;\n\n    .form-item {\n      margin-bottom: 40rpx;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .label {\n        display: block;\n        font-size: 28rpx;\n        color: #333;\n        margin-bottom: 16rpx;\n\n        .required {\n          color: #ff4757;\n        }\n      }\n\n      .input {\n        width: 100%;\n        height: 80rpx;\n        padding: 0 24rpx;\n        border: 2rpx solid #e9ecef;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background: #f8f9fa;\n      }\n\n      .textarea {\n        width: 100%;\n        min-height: 160rpx;\n        padding: 24rpx;\n        border: 2rpx solid #e9ecef;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background: #f8f9fa;\n        resize: none;\n      }\n\n      .char-count {\n        display: block;\n        text-align: right;\n        font-size: 24rpx;\n        color: #999;\n        margin-top: 8rpx;\n      }\n\n      &.avatar-item {\n        .avatar-upload {\n          width: 120rpx;\n          height: 120rpx;\n          border-radius: 60rpx;\n          border: 2rpx solid #e9ecef;\n          overflow: hidden;\n\n          .avatar-img {\n            width: 100%;\n            height: 100%;\n          }\n\n          .avatar-placeholder {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            background: #f8f9fa;\n\n            .iconfont {\n              font-size: 32rpx;\n              color: #999;\n              margin-bottom: 8rpx;\n            }\n\n            .placeholder-text {\n              font-size: 20rpx;\n              color: #999;\n            }\n          }\n        }\n      }\n\n      .code-input {\n        display: flex;\n        align-items: center;\n        gap: 16rpx;\n\n        .input {\n          flex: 1;\n        }\n\n        .code-btn {\n          width: 200rpx;\n          height: 80rpx;\n          background: #007aff;\n          color: #fff;\n          border: none;\n          border-radius: 12rpx;\n          font-size: 24rpx;\n\n          &:disabled {\n            background: #ccc;\n          }\n        }\n      }\n\n      .radio-group {\n        display: flex;\n        gap: 40rpx;\n\n        .radio-item {\n          display: flex;\n          align-items: center;\n          gap: 8rpx;\n          font-size: 28rpx;\n          color: #333;\n        }\n      }\n\n      .salary-input {\n        display: flex;\n        align-items: center;\n        gap: 16rpx;\n\n        .salary-min,\n        .salary-max {\n          flex: 1;\n        }\n\n        .separator {\n          font-size: 28rpx;\n          color: #666;\n        }\n\n        .unit {\n          font-size: 28rpx;\n          color: #666;\n        }\n      }\n\n      .picker-input {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        height: 80rpx;\n        padding: 0 24rpx;\n        border: 2rpx solid #e9ecef;\n        border-radius: 12rpx;\n        background: #f8f9fa;\n\n        .picker-text {\n          font-size: 28rpx;\n          color: #333;\n        }\n\n        .iconfont {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n    }\n  }\n\n  .agreement {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 40rpx;\n    font-size: 24rpx;\n    color: #666;\n\n    checkbox-group {\n      margin-right: 16rpx;\n      margin-top: 4rpx;\n    }\n\n    .agreement-text {\n      flex: 1;\n      line-height: 1.5;\n\n      .link {\n        color: #007aff;\n        text-decoration: underline;\n      }\n    }\n  }\n\n  .register-btn {\n    width: 100%;\n    height: 88rpx;\n    background: #007aff;\n    color: #fff;\n    border: none;\n    border-radius: 44rpx;\n    font-size: 32rpx;\n    font-weight: 500;\n\n    &:disabled {\n      background: #ccc;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=6e828681&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=6e828681&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756738041713\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}